# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Database Setup
```bash
# Database recreation (PostgreSQL)
drop database sanka;
create database sanka;

# Run migrations and setup
python manage.py migrate
python manage.py createsuperuser
python manage.py verify_superuser

# Load initial data
python manage.py load_subscriptions
python manage.py load_actions
python manage.py onetime_create_master_pdf
```

After creating initial data, navigate to http://app.localhost:8000/manage/data/ and load fixture files.

### Running the Application

#### Development Mode
```bash
# Standard Django development server
python manage.py runserver

# ASGI server with async/streaming support (CURRENTLY RUNNING)
uvicorn sanka.asgi:application --host 0.0.0.0 --port 8000 --reload --reload-include "*.html"
```

#### Frontend Development (SAN AI React App)
The `san-ai/` directory contains a modern React + TypeScript + Vite application that serves as the AI chat interface.

```bash
# Setup environment variables in san-ai/.env and san-ai/.env.production
# VITE_SANKA_URL=http://app.localhost:8000

# Install dependencies
cd san-ai
npm install

# Run React development server
npm run dev
# Or from root directory:
npm run san:dev

# Build for production (check for linting errors)
npm run build
```

**SAN AI Architecture:**
- **State Management**: Zustand stores for UI state
- **UI Components**: Custom React components with Tailwind CSS
- **Chat Features**: Real-time messaging with SSE, file uploads, context management
- **Build Output**: Builds to `../static/san-ai/` for Django integration
- **Development**: Vite dev server with HMR and CORS configured

### Testing and Quality
```bash
# Run unit tests with coverage
python -m coverage run manage.py test --settings=data.tests.test_settings --keepdb -v 3

# Tailwind CSS development
npm run dev  # Watch mode
npm run build  # Production build

# Cypress testing
npm run cy:open  # Interactive mode
npm run cy:e2e:chrome  # Headless Chrome
```

### Background Jobs (Hatchet)
```bash
# Legacy method (still works but deprecated)
python manage.py hatchet_worker --worker-name production-worker --debug

# NEW: Modern Hatchet worker (recommended)
python -m hatchet.main  # Start both main and scheduled workers
python -m hatchet.main --main_worker  # Start only main worker
python -m hatchet.main --schedule_worker  # Start only scheduled worker
```

## Architecture Overview

### Technology Stack
- **Backend**: Django 4.2.20 with PostgreSQL
- **Frontend**: React (SAN AI) + Django templates with HTMX
- **Background Jobs**: Hatchet SDK for distributed task processing
- **Styling**: Tailwind CSS
- **File Storage**: DigitalOcean Spaces (S3-compatible)
- **Authentication**: Django Auth + SAML support
- **API**: Django REST Framework with JWT

### Application Structure

#### Core Django Apps
- `data/` - Main business logic and models
- `sanka/` - Django project settings and configuration
- `utils/` - Shared utilities and helper functions
- `action/` - Workflow and automation system
- `templates/` - Django templates

#### Key Data Modules
- `data/accounts/` - User accounts, workspaces, billing
- `data/contact/` - Contact management and CRM
- `data/commerce/` - E-commerce and order processing
- `data/inventory/` - Inventory management
- `data/invoice/` - Invoicing system
- `data/custom_object/` - Dynamic custom objects
- `data/workflow/` - Workflow automation

#### Background Processing
The application uses Hatchet for background job processing:
- **New structure**: `hatchet/` directory with modular worker system
- **Legacy**: `app.py` and `bgjob/` directory (being phased out)
- **Worker types**: Main worker (background jobs) and scheduled worker (cron tasks)
- Handles imports, exports, integrations, and scheduled tasks
- Supports both web mode and lightweight Hatchet worker mode
- Environment variable `HATCHET=True` switches to worker-only mode

### Settings Architecture
Django settings support dual modes:
- **Web Mode** (`HATCHET=False`): Full Django application
- **Hatchet Mode** (`HATCHET=True`): Lightweight background worker

Key environment flags:
- `PROD`, `STAGING`, `LOCAL` - Environment detection
- `DEBUG` - Debug mode
- `HATCHET` - Background worker mode
- `RUN_HATCHET_SCHEDULER` - Enable scheduled tasks

### Integration Ecosystem
The application integrates with 30+ external services:
- **CRM**: HubSpot, Salesforce
- **E-commerce**: Shopify, Square, Stripe, Amazon, eBay
- **Accounting**: Freee, MoneyForward
- **Communication**: Slack, Discord, Twilio, SendGrid
- **Cloud**: Azure, Google Workspace, AWS

### File Storage Strategy
- **Static files**: Local development, DigitalOcean Spaces in production
- **Media files**: Always DigitalOcean Spaces
- **Private files**: Separate storage backend for sensitive data

### Database Patterns
- Uses Django ORM with PostgreSQL-specific features
- Custom field types and extensive migrations
- Background job status tracking
- Audit trails and versioning for key entities

### Development Patterns
- Environment-based configuration using django-environ
- Extensive middleware for security, logging, and request processing
- Custom template tags and filters
- Background job integration throughout the application
- HTMX for dynamic frontend interactions

## Common Workflows

### Adding New Features
1. Create Django models in appropriate `data/` subdirectory
2. Generate and run migrations: `python manage.py makemigrations && python manage.py migrate`
3. Add views, templates, and URL patterns
4. Create background tasks if needed in the relevant background module
5. Update tests and run quality checks

### Integration Development
1. Add API credentials to settings.py
2. Create utility functions in `utils/` directory
3. Implement background sync tasks using Hatchet
4. Add webhook handlers if required
5. Test with development credentials

### Background Task Development
1. Define tasks in relevant `background/` modules
2. Add task to worker list in `app.py`
3. Create Django management commands for triggering
4. Test with `python manage.py hatchet_worker`